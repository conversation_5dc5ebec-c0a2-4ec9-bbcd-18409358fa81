# Análise QANX vs BTC - Dashboard de Correlação

Este projeto implementa uma análise completa da correlação entre os tokens QANX e BTC, testando a teoria de que o fundo do QANX é manipulado baseado nos ciclos do Bitcoin.

## Teoria Testada

A hipótese é que quando o Bitcoin tem uma alta (bitcoin season), os operadores do fundo QANX:
1. Usam o fundo para comprar BTC
2. Quando BTC começa a cair, retiram o lucro
3. Jogam o lucro para a reserva de QANX para aumentar o valor do token

## Funcionalidades

- **Coleta de Dados**: Baixa dados históricos completos do QANX e BTC via API CoinGecko
- **Análise Estatística**: Calcula correlações, identifica ciclos do BTC, testa hipóteses
- **Dashboard Interativo**: Visualização web com gráficos dinâmicos e insights

## Instalação

1. Clone o repositório:
```bash
git clone <repo-url>
cd data
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

## Uso

### Opção 1: Executar tudo de uma vez
```bash
python main.py --all
```

### Opção 2: Executar por etapas

1. **Coletar dados históricos**:
```bash
python main.py --collect
```

2. **Executar análise**:
```bash
python main.py --analyze
```

3. **Iniciar dashboard**:
```bash
python main.py --dashboard
```

### Opção 3: Executar scripts individuais

```bash
# Coletar dados
python data_collector.py

# Executar análise
python analyzer.py

# Iniciar dashboard
python dashboard.py
```

## Estrutura do Projeto

```
├── main.py              # Script principal
├── data_collector.py    # Coleta dados históricos
├── analyzer.py          # Análise estatística
├── dashboard.py         # Dashboard web
├── utils.py             # Funções utilitárias
├── config.py            # Configurações
├── requirements.txt     # Dependências
├── data/               # Dados coletados
│   ├── btc_historical.csv
│   ├── qanx_historical.csv
│   └── analysis_results.csv
└── README.md           # Este arquivo
```

## Análises Implementadas

### 1. Correlação
- Correlação de preços
- Correlação de retornos
- Correlação móvel (janela configurável)
- Análise de lag temporal

### 2. Ciclos do Bitcoin
- Identificação de bull/bear markets
- Performance do QANX em diferentes ciclos
- Análise de timing entre movimentos

### 3. Teste da Teoria de Manipulação
- Comportamento do QANX após grandes movimentos do BTC
- Análise estatística de significância
- Padrões de volume durante movimentos

### 4. Métricas de Performance
- Retorno total e anualizado
- Volatilidade
- Sharpe ratio
- Maximum drawdown
- Win rate

## Dashboard

O dashboard web inclui:

- **Cards de Métricas**: Correlação atual, preços, período de análise
- **Gráfico de Preços**: Evolução temporal normalizada e absoluta
- **Correlação Móvel**: Evolução da correlação ao longo do tempo
- **Retornos Diários**: Comparação de volatilidade
- **Análise de Volume**: Padrões de negociação
- **Scatter Plot**: Correlação visual de retornos
- **Insights Automáticos**: Conclusões baseadas na análise

### Controles Interativos
- Seletor de período de análise
- Ajuste da janela de correlação móvel
- Filtros por data

## Acesso ao Dashboard

Após iniciar o dashboard, acesse:
```
http://127.0.0.1:8050
```

## APIs Utilizadas

- **CoinGecko API**: Dados históricos gratuitos
- Limite de rate: ~50 requests/minuto
- Dados disponíveis: preço, volume, market cap

## Dependências Principais

- `pandas`: Manipulação de dados
- `numpy`: Cálculos numéricos
- `plotly`: Gráficos interativos
- `dash`: Framework web
- `scipy`: Análise estatística
- `requests`: Requisições HTTP

## Resultados Esperados

A análise fornecerá:

1. **Correlação Quantificada**: Grau de correlação entre QANX e BTC
2. **Evidência da Teoria**: Testes estatísticos da hipótese de manipulação
3. **Padrões Temporais**: Identificação de lags e ciclos
4. **Insights Acionáveis**: Conclusões para estratégias de trading

## Limitações

- Dados limitados pela disponibilidade da API
- Correlação não implica causalidade
- Mercado de criptomoedas é altamente volátil
- Análise baseada apenas em dados de preço/volume

## Próximos Passos

- Integração com mais exchanges
- Análise de dados on-chain
- Machine learning para predição
- Alertas automáticos de correlação

## Suporte

Para dúvidas ou problemas:
1. Verifique se todas as dependências estão instaladas
2. Confirme conexão com internet para APIs
3. Verifique logs de erro no terminal
