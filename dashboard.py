"""
Dashboard web para análise QANX vs BTC
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import dash_bootstrap_components as dbc

from utils import load_data, calculate_returns, calculate_correlation
from config import COLORS, DASH_HOST, DASH_PORT, DASH_DEBUG
from analyzer import QANXBTCAnalyzer

# Inicializa o app Dash
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
app.title = "QANX vs BTC Analysis Dashboard"

# Carrega dados
try:
    analyzer = QANXBTCAnalyzer()
    analyzer.load_data()
    merged_data = analyzer.merged_data

    # Se não há dados, cria dados sintéticos para demonstração
    if merged_data.empty:
        print("Criando dados sintéticos para demonstração do dashboard...")
        dates = pd.date_range(end=pd.Timestamp.now(), periods=365, freq='D')

        import numpy as np
        np.random.seed(42)

        # Dados sintéticos do BTC
        btc_prices = []
        btc_price = 50000
        for i in range(365):
            change = np.random.normal(0.001, 0.03)
            btc_price = btc_price * (1 + change)
            btc_prices.append(btc_price)

        # Dados sintéticos do QANX (correlacionados com BTC)
        qanx_prices = []
        qanx_price = 0.1
        for i in range(365):
            btc_change = (btc_prices[i] / btc_prices[i-1] - 1) if i > 0 else 0
            qanx_change = btc_change * 0.7 + np.random.normal(0, 0.05)  # 70% correlação
            qanx_price = qanx_price * (1 + qanx_change)
            qanx_prices.append(qanx_price)

        merged_data = pd.DataFrame({
            'price_btc': btc_prices,
            'price_qanx': qanx_prices,
            'volume_btc': np.random.uniform(1e9, 5e9, 365),
            'volume_qanx': np.random.uniform(1e6, 1e7, 365),
            'market_cap_btc': [p * 19e6 for p in btc_prices],
            'market_cap_qanx': [p * 1e9 for p in qanx_prices]
        }, index=dates)

    # Calcula métricas básicas
    btc_returns = calculate_returns(merged_data['price_btc'])
    qanx_returns = calculate_returns(merged_data['price_qanx'])
    correlation = btc_returns.corr(qanx_returns)
    rolling_corr = calculate_correlation(qanx_returns, btc_returns, window=30)

except Exception as e:
    print(f"Erro ao carregar dados: {e}")
    merged_data = pd.DataFrame()
    correlation = 0
    rolling_corr = pd.Series()

# Layout do dashboard
app.layout = dbc.Container([
    dbc.Row([
        dbc.Col([
            html.H1("QANX vs BTC Analysis Dashboard", 
                   className="text-center mb-4",
                   style={'color': COLORS['text']}),
            html.Hr()
        ])
    ]),
    
    # Cards com métricas principais
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Correlação Atual", className="card-title"),
                    html.H2(f"{correlation:.3f}", className="text-primary")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Preço BTC", className="card-title"),
                    html.H2(f"${merged_data['price_btc'].iloc[-1]:,.2f}" if not merged_data.empty else "$0", 
                            className="text-warning")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Preço QANX", className="card-title"),
                    html.H2(f"${merged_data['price_qanx'].iloc[-1]:.6f}" if not merged_data.empty else "$0", 
                            className="text-info")
                ])
            ])
        ], width=3),
        
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("Período Análise", className="card-title"),
                    html.H2(f"{len(merged_data)} dias" if not merged_data.empty else "0 dias", 
                            className="text-success")
                ])
            ])
        ], width=3),
    ], className="mb-4"),
    
    # Gráfico principal de preços
    dbc.Row([
        dbc.Col([
            dcc.Graph(id="price-chart")
        ])
    ], className="mb-4"),
    
    # Gráficos de análise
    dbc.Row([
        dbc.Col([
            dcc.Graph(id="correlation-chart")
        ], width=6),
        dbc.Col([
            dcc.Graph(id="returns-chart")
        ], width=6)
    ], className="mb-4"),
    
    # Gráfico de volume e scatter plot
    dbc.Row([
        dbc.Col([
            dcc.Graph(id="volume-chart")
        ], width=6),
        dbc.Col([
            dcc.Graph(id="scatter-chart")
        ], width=6)
    ], className="mb-4"),
    
    # Controles
    dbc.Row([
        dbc.Col([
            html.Label("Período para análise:"),
            dcc.DatePickerRange(
                id='date-picker-range',
                start_date=merged_data.index.min() if not merged_data.empty else datetime.now() - timedelta(days=365),
                end_date=merged_data.index.max() if not merged_data.empty else datetime.now(),
                display_format='DD/MM/YYYY'
            )
        ], width=6),
        dbc.Col([
            html.Label("Janela de correlação (dias):"),
            dcc.Slider(
                id='correlation-window',
                min=7,
                max=90,
                step=7,
                value=30,
                marks={i: str(i) for i in range(7, 91, 14)}
            )
        ], width=6)
    ], className="mb-4"),
    
    # Insights
    dbc.Row([
        dbc.Col([
            html.H3("Insights da Análise"),
            html.Div(id="insights-content")
        ])
    ])
    
], fluid=True, style={'backgroundColor': COLORS['background'], 'color': COLORS['text']})

# Callbacks para atualizar gráficos
@app.callback(
    Output('price-chart', 'figure'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_price_chart(start_date, end_date):
    if merged_data.empty:
        return go.Figure()
    
    # Filtra dados por período
    mask = (merged_data.index >= start_date) & (merged_data.index <= end_date)
    filtered_data = merged_data.loc[mask]
    
    # Cria subplots
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Preços Normalizados', 'Preços Absolutos'),
        vertical_spacing=0.1
    )
    
    # Normaliza preços para comparação
    btc_norm = (filtered_data['price_btc'] / filtered_data['price_btc'].iloc[0]) * 100
    qanx_norm = (filtered_data['price_qanx'] / filtered_data['price_qanx'].iloc[0]) * 100
    
    # Gráfico normalizado
    fig.add_trace(
        go.Scatter(x=filtered_data.index, y=btc_norm, name='BTC (Normalizado)', 
                  line=dict(color=COLORS['btc'])),
        row=1, col=1
    )
    fig.add_trace(
        go.Scatter(x=filtered_data.index, y=qanx_norm, name='QANX (Normalizado)', 
                  line=dict(color=COLORS['qanx'])),
        row=1, col=1
    )
    
    # Gráfico absoluto (eixos separados)
    fig.add_trace(
        go.Scatter(x=filtered_data.index, y=filtered_data['price_btc'], name='BTC ($)', 
                  line=dict(color=COLORS['btc']), yaxis='y3'),
        row=2, col=1
    )
    fig.add_trace(
        go.Scatter(x=filtered_data.index, y=filtered_data['price_qanx'], name='QANX ($)', 
                  line=dict(color=COLORS['qanx']), yaxis='y4'),
        row=2, col=1
    )
    
    fig.update_layout(
        title="Evolução dos Preços BTC vs QANX",
        template="plotly_dark",
        height=600
    )
    
    return fig

@app.callback(
    Output('correlation-chart', 'figure'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date'),
     Input('correlation-window', 'value')]
)
def update_correlation_chart(start_date, end_date, window):
    if merged_data.empty:
        return go.Figure()
    
    # Filtra dados
    mask = (merged_data.index >= start_date) & (merged_data.index <= end_date)
    filtered_data = merged_data.loc[mask]
    
    # Calcula correlação móvel
    btc_returns = calculate_returns(filtered_data['price_btc'])
    qanx_returns = calculate_returns(filtered_data['price_qanx'])
    rolling_corr = calculate_correlation(qanx_returns, btc_returns, window=window)
    
    fig = go.Figure()
    fig.add_trace(
        go.Scatter(x=rolling_corr.index, y=rolling_corr, 
                  name=f'Correlação Móvel ({window}d)',
                  line=dict(color=COLORS['qanx']))
    )
    
    # Linha de referência em zero
    fig.add_hline(y=0, line_dash="dash", line_color="gray")
    fig.add_hline(y=0.5, line_dash="dot", line_color="green", annotation_text="Correlação Forte")
    fig.add_hline(y=-0.5, line_dash="dot", line_color="red", annotation_text="Correlação Negativa Forte")
    
    fig.update_layout(
        title=f"Correlação Móvel QANX vs BTC ({window} dias)",
        yaxis_title="Correlação",
        template="plotly_dark"
    )
    
    return fig

@app.callback(
    Output('returns-chart', 'figure'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_returns_chart(start_date, end_date):
    if merged_data.empty:
        return go.Figure()
    
    # Filtra dados
    mask = (merged_data.index >= start_date) & (merged_data.index <= end_date)
    filtered_data = merged_data.loc[mask]
    
    btc_returns = calculate_returns(filtered_data['price_btc']) * 100
    qanx_returns = calculate_returns(filtered_data['price_qanx']) * 100
    
    fig = go.Figure()
    fig.add_trace(
        go.Scatter(x=btc_returns.index, y=btc_returns, 
                  name='BTC Returns (%)', line=dict(color=COLORS['btc']))
    )
    fig.add_trace(
        go.Scatter(x=qanx_returns.index, y=qanx_returns, 
                  name='QANX Returns (%)', line=dict(color=COLORS['qanx']))
    )
    
    fig.update_layout(
        title="Retornos Diários (%)",
        yaxis_title="Retorno (%)",
        template="plotly_dark"
    )
    
    return fig

@app.callback(
    Output('volume-chart', 'figure'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_volume_chart(start_date, end_date):
    if merged_data.empty:
        return go.Figure()
    
    # Filtra dados
    mask = (merged_data.index >= start_date) & (merged_data.index <= end_date)
    filtered_data = merged_data.loc[mask]
    
    fig = make_subplots(rows=2, cols=1, subplot_titles=('Volume BTC', 'Volume QANX'))
    
    fig.add_trace(
        go.Bar(x=filtered_data.index, y=filtered_data['volume_btc'], 
               name='Volume BTC', marker_color=COLORS['btc']),
        row=1, col=1
    )
    fig.add_trace(
        go.Bar(x=filtered_data.index, y=filtered_data['volume_qanx'], 
               name='Volume QANX', marker_color=COLORS['qanx']),
        row=2, col=1
    )
    
    fig.update_layout(
        title="Volume de Negociação",
        template="plotly_dark",
        height=500
    )
    
    return fig

@app.callback(
    Output('scatter-chart', 'figure'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_scatter_chart(start_date, end_date):
    if merged_data.empty:
        return go.Figure()
    
    # Filtra dados
    mask = (merged_data.index >= start_date) & (merged_data.index <= end_date)
    filtered_data = merged_data.loc[mask]
    
    btc_returns = calculate_returns(filtered_data['price_btc']) * 100
    qanx_returns = calculate_returns(filtered_data['price_qanx']) * 100
    
    fig = go.Figure()
    fig.add_trace(
        go.Scatter(x=btc_returns, y=qanx_returns, 
                  mode='markers', name='Retornos Diários',
                  marker=dict(color=COLORS['qanx'], opacity=0.6))
    )
    
    # Linha de tendência
    if len(btc_returns) > 1:
        z = np.polyfit(btc_returns.dropna(), qanx_returns.dropna(), 1)
        p = np.poly1d(z)
        fig.add_trace(
            go.Scatter(x=btc_returns, y=p(btc_returns), 
                      mode='lines', name='Tendência',
                      line=dict(color='red', dash='dash'))
        )
    
    fig.update_layout(
        title="Correlação de Retornos: QANX vs BTC",
        xaxis_title="Retorno BTC (%)",
        yaxis_title="Retorno QANX (%)",
        template="plotly_dark"
    )
    
    return fig

@app.callback(
    Output('insights-content', 'children'),
    [Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_insights(start_date, end_date):
    if merged_data.empty:
        return html.P("Dados não disponíveis")
    
    # Executa análise rápida
    try:
        analyzer_temp = QANXBTCAnalyzer()
        analyzer_temp.merged_data = merged_data
        analyzer_temp.analyze_correlation()
        analyzer_temp.analyze_btc_seasons_impact()
        analyzer_temp.test_manipulation_theory()
        insights = analyzer_temp.generate_insights()
        
        return [html.P(f"• {insight}") for insight in insights]
    except:
        return html.P("Erro ao gerar insights")

if __name__ == '__main__':
    app.run(host=DASH_HOST, port=DASH_PORT, debug=DASH_DEBUG)
