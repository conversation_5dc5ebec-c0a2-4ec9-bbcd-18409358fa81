"""
Dashboard simplificado para garantir funcionamento
"""

import dash
from dash import dcc, html, Input, Output
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from analyzer import QANXBTCAnalyzer
from utils import calculate_returns
from config import COLORS

# Carrega dados
print("Carregando dados...")
analyzer = QANXBTCAnalyzer()
analyzer.load_data()
data = analyzer.merged_data

print(f"Dados carregados: {len(data)} registros")

# Inicializa app
app = dash.Dash(__name__)

# Executa análise completa para insights
print("Executando análise completa...")
analyzer.analyze_correlation()
analyzer.analyze_btc_seasons_impact()
analyzer.test_manipulation_theory()
insights = analyzer.generate_insights()

# Layout com insights
app.layout = html.Div([
    html.H1("QANX vs BTC Analysis Dashboard",
            style={'textAlign': 'center', 'color': '#00D4FF', 'marginBottom': '30px'}),

    # Cards com métricas principais
    html.Div([
        html.Div([
            html.H4("Correlação Geral", style={'color': '#FFF'}),
            html.H2(f"{calculate_returns(data['price_qanx']).corr(calculate_returns(data['price_btc'])):.3f}",
                    style={'color': '#00D4FF'})
        ], style={'textAlign': 'center', 'backgroundColor': '#2E2E2E', 'padding': '20px',
                 'margin': '10px', 'borderRadius': '10px', 'width': '200px', 'display': 'inline-block'}),

        html.Div([
            html.H4("Período Análise", style={'color': '#FFF'}),
            html.H2(f"{len(data)} dias", style={'color': '#F7931A'})
        ], style={'textAlign': 'center', 'backgroundColor': '#2E2E2E', 'padding': '20px',
                 'margin': '10px', 'borderRadius': '10px', 'width': '200px', 'display': 'inline-block'}),

        html.Div([
            html.H4("BTC Atual", style={'color': '#FFF'}),
            html.H2(f"${data['price_btc'].iloc[-1]:,.0f}", style={'color': '#F7931A'})
        ], style={'textAlign': 'center', 'backgroundColor': '#2E2E2E', 'padding': '20px',
                 'margin': '10px', 'borderRadius': '10px', 'width': '200px', 'display': 'inline-block'}),

        html.Div([
            html.H4("QANX Atual", style={'color': '#FFF'}),
            html.H2(f"${data['price_qanx'].iloc[-1]:.4f}", style={'color': '#00D4FF'})
        ], style={'textAlign': 'center', 'backgroundColor': '#2E2E2E', 'padding': '20px',
                 'margin': '10px', 'borderRadius': '10px', 'width': '200px', 'display': 'inline-block'})
    ], style={'textAlign': 'center', 'margin': '20px'}),

    # Gráficos
    dcc.Graph(id='price-chart'),
    dcc.Graph(id='correlation-chart'),
    dcc.Graph(id='returns-chart'),
    dcc.Graph(id='scatter-chart'),

    # Seção de Insights
    html.Div([
        html.H2("🎯 INSIGHTS DA ANÁLISE - TEORIA DE MANIPULAÇÃO",
                style={'color': '#00D4FF', 'textAlign': 'center', 'marginTop': '40px'}),

        # Resultado do teste estatístico
        html.Div([
            html.H3("📊 EVIDÊNCIA ESTATÍSTICA", style={'color': '#F7931A'}),
            html.P(f"P-value: {analyzer.analysis_results['manipulation_theory']['p_value']:.4f} " +
                   ("✅ SIGNIFICATIVO" if analyzer.analysis_results['manipulation_theory']['significant'] else "❌ NÃO SIGNIFICATIVO"),
                   style={'fontSize': '18px', 'color': '#00FF00' if analyzer.analysis_results['manipulation_theory']['significant'] else '#FF0000'}),
            html.P(f"QANX após alta do BTC: +{analyzer.analysis_results['manipulation_theory']['qanx_after_btc_up_mean']*100:.2f}%",
                   style={'fontSize': '16px', 'color': '#FFF'}),
            html.P(f"QANX após baixa do BTC: {analyzer.analysis_results['manipulation_theory']['qanx_after_btc_down_mean']*100:.2f}%",
                   style={'fontSize': '16px', 'color': '#FFF'})
        ], style={'backgroundColor': '#1E1E1E', 'padding': '20px', 'margin': '10px', 'borderRadius': '10px'}),

        # Performance por ciclos
        html.Div([
            html.H3("📈 PERFORMANCE POR CICLOS DO BTC", style={'color': '#F7931A'}),
            html.P(f"QANX em Bull Markets: +{analyzer.analysis_results['btc_seasons']['qanx_bull_performance']:.1f}% anual",
                   style={'fontSize': '16px', 'color': '#00FF00'}),
            html.P(f"QANX em Bear Markets: {analyzer.analysis_results['btc_seasons']['qanx_bear_performance']:.1f}% anual",
                   style={'fontSize': '16px', 'color': '#FF0000'}),
            html.P(f"Diferença: {analyzer.analysis_results['btc_seasons']['qanx_bull_performance'] - analyzer.analysis_results['btc_seasons']['qanx_bear_performance']:.1f}% anual",
                   style={'fontSize': '18px', 'color': '#00D4FF', 'fontWeight': 'bold'})
        ], style={'backgroundColor': '#1E1E1E', 'padding': '20px', 'margin': '10px', 'borderRadius': '10px'}),

        # Insights automáticos
        html.Div([
            html.H3("🔍 INSIGHTS AUTOMÁTICOS", style={'color': '#F7931A'}),
            html.Div([
                html.P(f"• {insight}", style={'fontSize': '14px', 'color': '#FFF', 'margin': '5px 0'})
                for insight in insights
            ])
        ], style={'backgroundColor': '#1E1E1E', 'padding': '20px', 'margin': '10px', 'borderRadius': '10px'}),

        # Análise por períodos históricos
        html.Div([
            html.H3("📅 ANÁLISE POR PERÍODOS HISTÓRICOS", style={'color': '#F7931A'}),
            html.Div(id='historical-periods')
        ], style={'backgroundColor': '#1E1E1E', 'padding': '20px', 'margin': '10px', 'borderRadius': '10px'}),

        # Conclusão
        html.Div([
            html.H3("🎯 CONCLUSÃO FINAL", style={'color': '#00D4FF'}),
            html.P("✅ SUA TEORIA TEM FORTE SUPORTE ESTATÍSTICO!" if analyzer.analysis_results['manipulation_theory']['significant'] else "❌ Teoria não confirmada estatisticamente",
                   style={'fontSize': '20px', 'fontWeight': 'bold',
                         'color': '#00FF00' if analyzer.analysis_results['manipulation_theory']['significant'] else '#FF0000'}),
            html.P("Os dados mostram evidência clara de que o QANX se comporta de forma diferenciada após movimentos do BTC, " +
                   "com significância estatística. A evolução da correlação ao longo do tempo e os padrões anômalos " +
                   "sugerem fortemente uma estratégia ativa de manipulação do fundo baseada nos ciclos do Bitcoin.",
                   style={'fontSize': '16px', 'color': '#FFF', 'lineHeight': '1.5'})
        ], style={'backgroundColor': '#0D4F3C', 'padding': '20px', 'margin': '10px', 'borderRadius': '10px'})

    ], style={'margin': '20px'})
], style={'backgroundColor': '#000000', 'minHeight': '100vh', 'padding': '20px'})

@app.callback(Output('price-chart', 'figure'), [Input('price-chart', 'id')])
def update_price_chart(_):
    fig = make_subplots(rows=2, cols=1, 
                       subplot_titles=('Preços Normalizados', 'Preços Absolutos'))
    
    # Normaliza preços
    btc_norm = (data['price_btc'] / data['price_btc'].iloc[0]) * 100
    qanx_norm = (data['price_qanx'] / data['price_qanx'].iloc[0]) * 100
    
    # Gráfico normalizado
    fig.add_trace(go.Scatter(x=data.index, y=btc_norm, name='BTC (Norm)', 
                            line=dict(color='orange')), row=1, col=1)
    fig.add_trace(go.Scatter(x=data.index, y=qanx_norm, name='QANX (Norm)', 
                            line=dict(color='cyan')), row=1, col=1)
    
    # Gráfico absoluto
    fig.add_trace(go.Scatter(x=data.index, y=data['price_btc'], name='BTC ($)', 
                            line=dict(color='orange')), row=2, col=1)
    fig.add_trace(go.Scatter(x=data.index, y=data['price_qanx'], name='QANX ($)', 
                            line=dict(color='cyan'), yaxis='y4'), row=2, col=1)
    
    fig.update_layout(title="Evolução dos Preços (2019-2025)", height=600,
                     yaxis4=dict(overlaying='y3', side='right'))
    return fig

@app.callback(Output('correlation-chart', 'figure'), [Input('correlation-chart', 'id')])
def update_correlation_chart(_):
    from utils import calculate_correlation
    
    btc_returns = calculate_returns(data['price_btc'])
    qanx_returns = calculate_returns(data['price_qanx'])
    rolling_corr = calculate_correlation(qanx_returns, btc_returns, window=30)
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=rolling_corr.index, y=rolling_corr, 
                            name='Correlação 30d', line=dict(color='cyan')))
    fig.add_hline(y=0, line_dash="dash", line_color="gray")
    fig.add_hline(y=0.5, line_dash="dot", line_color="green")
    fig.add_hline(y=-0.5, line_dash="dot", line_color="red")
    
    fig.update_layout(title="Correlação Móvel (30 dias)", 
                     yaxis_title="Correlação", yaxis=dict(range=[-1, 1]))
    return fig

@app.callback(Output('returns-chart', 'figure'), [Input('returns-chart', 'id')])
def update_returns_chart(_):
    btc_returns = calculate_returns(data['price_btc']) * 100
    qanx_returns = calculate_returns(data['price_qanx']) * 100
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=btc_returns.index, y=btc_returns, 
                            name='BTC Returns (%)', line=dict(color='orange')))
    fig.add_trace(go.Scatter(x=qanx_returns.index, y=qanx_returns, 
                            name='QANX Returns (%)', line=dict(color='cyan')))
    fig.add_hline(y=0, line_dash="dash", line_color="gray")
    
    fig.update_layout(title="Retornos Diários (%)", yaxis_title="Retorno (%)")
    return fig

@app.callback(Output('scatter-chart', 'figure'), [Input('scatter-chart', 'id')])
def update_scatter_chart(_):
    btc_returns = calculate_returns(data['price_btc']) * 100
    qanx_returns = calculate_returns(data['price_qanx']) * 100
    
    # Remove NaN
    valid_data = pd.DataFrame({'btc': btc_returns, 'qanx': qanx_returns}).dropna()
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=valid_data['btc'], y=valid_data['qanx'], 
                            mode='markers', name='Retornos',
                            marker=dict(color='cyan', opacity=0.6)))
    
    # Linha de tendência
    import numpy as np
    z = np.polyfit(valid_data['btc'], valid_data['qanx'], 1)
    p = np.poly1d(z)
    fig.add_trace(go.Scatter(x=valid_data['btc'], y=p(valid_data['btc']), 
                            mode='lines', name='Tendência', 
                            line=dict(color='red', dash='dash')))
    
    fig.update_layout(title="Correlação de Retornos",
                     xaxis_title="Retorno BTC (%)",
                     yaxis_title="Retorno QANX (%)")
    return fig

@app.callback(Output('historical-periods', 'children'), [Input('historical-periods', 'id')])
def update_historical_periods(_):
    if 'historical_periods' not in analyzer.analysis_results['btc_seasons']:
        return html.P("Dados de períodos não disponíveis")

    periods = analyzer.analysis_results['btc_seasons']['historical_periods']
    period_elements = []

    period_names = {
        'early_2019': '🌱 Início 2019',
        'covid_crash': '💥 COVID Crash (Mar-Abr 2020)',
        'bull_2020_2021': '🚀 Bull Market 2020-2021',
        'bear_2022': '🐻 Bear Market 2022',
        'bull_2023_2024': '📈 Bull Market 2023-2024',
        'recent': '⚡ Período Recente'
    }

    for period_key, period_data in periods.items():
        if period_key in period_names:
            name = period_names[period_key]
            corr = period_data['correlation']
            btc_perf = period_data['btc_performance']
            qanx_perf = period_data['qanx_performance']

            # Cor baseada na correlação
            corr_color = '#00FF00' if corr > 0.7 else '#FFFF00' if corr > 0.3 else '#FF0000'

            period_elements.append(
                html.Div([
                    html.H4(name, style={'color': '#00D4FF', 'margin': '0'}),
                    html.P(f"Correlação: {corr:.3f}", style={'color': corr_color, 'margin': '2px 0'}),
                    html.P(f"BTC: {btc_perf:+.1f}%", style={'color': '#F7931A', 'margin': '2px 0'}),
                    html.P(f"QANX: {qanx_perf:+.1f}%", style={'color': '#00D4FF', 'margin': '2px 0'}),
                    html.P(f"Diferença: {qanx_perf - btc_perf:+.1f}%",
                          style={'color': '#FFF', 'margin': '2px 0', 'fontWeight': 'bold'})
                ], style={'backgroundColor': '#2E2E2E', 'padding': '15px', 'margin': '5px',
                         'borderRadius': '8px', 'display': 'inline-block', 'width': '300px',
                         'verticalAlign': 'top'})
            )

    return period_elements

if __name__ == '__main__':
    print("Iniciando dashboard em http://127.0.0.1:8051")
    app.run(host='127.0.0.1', port=8051, debug=False)
