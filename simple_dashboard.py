"""
Dashboard simplificado para garantir funcionamento
"""

import dash
from dash import dcc, html, Input, Output
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from analyzer import QANXBTCAnalyzer
from utils import calculate_returns
from config import COLORS

# Carrega dados
print("Carregando dados...")
analyzer = QANXBTCAnalyzer()
analyzer.load_data()
data = analyzer.merged_data

print(f"Dados carregados: {len(data)} registros")

# Inicializa app
app = dash.Dash(__name__)

# Layout simples
app.layout = html.Div([
    html.H1("QANX vs BTC Analysis Dashboard", style={'textAlign': 'center'}),
    
    html.Div([
        html.H3(f"Correlação: {calculate_returns(data['price_qanx']).corr(calculate_returns(data['price_btc'])):.3f}"),
        html.H3(f"Período: {data.index.min().date()} a {data.index.max().date()}"),
        html.H3(f"Registros: {len(data)}")
    ], style={'textAlign': 'center', 'margin': '20px'}),
    
    dcc.Graph(id='price-chart'),
    dcc.Graph(id='correlation-chart'),
    dcc.Graph(id='returns-chart'),
    dcc.Graph(id='scatter-chart')
])

@app.callback(Output('price-chart', 'figure'), [Input('price-chart', 'id')])
def update_price_chart(_):
    fig = make_subplots(rows=2, cols=1, 
                       subplot_titles=('Preços Normalizados', 'Preços Absolutos'))
    
    # Normaliza preços
    btc_norm = (data['price_btc'] / data['price_btc'].iloc[0]) * 100
    qanx_norm = (data['price_qanx'] / data['price_qanx'].iloc[0]) * 100
    
    # Gráfico normalizado
    fig.add_trace(go.Scatter(x=data.index, y=btc_norm, name='BTC (Norm)', 
                            line=dict(color='orange')), row=1, col=1)
    fig.add_trace(go.Scatter(x=data.index, y=qanx_norm, name='QANX (Norm)', 
                            line=dict(color='cyan')), row=1, col=1)
    
    # Gráfico absoluto
    fig.add_trace(go.Scatter(x=data.index, y=data['price_btc'], name='BTC ($)', 
                            line=dict(color='orange')), row=2, col=1)
    fig.add_trace(go.Scatter(x=data.index, y=data['price_qanx'], name='QANX ($)', 
                            line=dict(color='cyan'), yaxis='y4'), row=2, col=1)
    
    fig.update_layout(title="Evolução dos Preços (2019-2025)", height=600,
                     yaxis4=dict(overlaying='y3', side='right'))
    return fig

@app.callback(Output('correlation-chart', 'figure'), [Input('correlation-chart', 'id')])
def update_correlation_chart(_):
    from utils import calculate_correlation
    
    btc_returns = calculate_returns(data['price_btc'])
    qanx_returns = calculate_returns(data['price_qanx'])
    rolling_corr = calculate_correlation(qanx_returns, btc_returns, window=30)
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=rolling_corr.index, y=rolling_corr, 
                            name='Correlação 30d', line=dict(color='cyan')))
    fig.add_hline(y=0, line_dash="dash", line_color="gray")
    fig.add_hline(y=0.5, line_dash="dot", line_color="green")
    fig.add_hline(y=-0.5, line_dash="dot", line_color="red")
    
    fig.update_layout(title="Correlação Móvel (30 dias)", 
                     yaxis_title="Correlação", yaxis=dict(range=[-1, 1]))
    return fig

@app.callback(Output('returns-chart', 'figure'), [Input('returns-chart', 'id')])
def update_returns_chart(_):
    btc_returns = calculate_returns(data['price_btc']) * 100
    qanx_returns = calculate_returns(data['price_qanx']) * 100
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=btc_returns.index, y=btc_returns, 
                            name='BTC Returns (%)', line=dict(color='orange')))
    fig.add_trace(go.Scatter(x=qanx_returns.index, y=qanx_returns, 
                            name='QANX Returns (%)', line=dict(color='cyan')))
    fig.add_hline(y=0, line_dash="dash", line_color="gray")
    
    fig.update_layout(title="Retornos Diários (%)", yaxis_title="Retorno (%)")
    return fig

@app.callback(Output('scatter-chart', 'figure'), [Input('scatter-chart', 'id')])
def update_scatter_chart(_):
    btc_returns = calculate_returns(data['price_btc']) * 100
    qanx_returns = calculate_returns(data['price_qanx']) * 100
    
    # Remove NaN
    valid_data = pd.DataFrame({'btc': btc_returns, 'qanx': qanx_returns}).dropna()
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=valid_data['btc'], y=valid_data['qanx'], 
                            mode='markers', name='Retornos',
                            marker=dict(color='cyan', opacity=0.6)))
    
    # Linha de tendência
    import numpy as np
    z = np.polyfit(valid_data['btc'], valid_data['qanx'], 1)
    p = np.poly1d(z)
    fig.add_trace(go.Scatter(x=valid_data['btc'], y=p(valid_data['btc']), 
                            mode='lines', name='Tendência', 
                            line=dict(color='red', dash='dash')))
    
    fig.update_layout(title="Correlação de Retornos", 
                     xaxis_title="Retorno BTC (%)", 
                     yaxis_title="Retorno QANX (%)")
    return fig

if __name__ == '__main__':
    print("Iniciando dashboard em http://127.0.0.1:8051")
    app.run(host='127.0.0.1', port=8051, debug=False)
